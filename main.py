from flask import Flask, request
from flask_restx import Api, Resource, fields
from utils import compute_indices_s2, compute_indices_s1

app = Flask(__name__)
api = Api(app, version='1.0', title='GEE Indices API',
          description='API pour calculer les indices Sentinel-1 et Sentinel-2 avec Google Earth Engine')

# AOI par défaut
default_aoi = [[[7.985805,35.662691],
                [7.986363,35.661652],
                [7.986878,35.661853],
                [7.987179,35.661993],
                [7.9875,35.662246],
                [7.98764,35.66256],
                [7.987211,35.663257],
                [7.986803,35.663205],
                [7.985805,35.662691]]]
default_uuid="550e8400-e29b-41d4-a716-************"
# Indices valides
valid_indices_s2 = ['NDVI', 'EVI', 'NDWI', 'NDMI', 'MNDWI', 'NDRE', 'NDREX', 'CCC', 'CWC',
                    'FAPAR', 'IRECI', 'LAI', 'MSAVI2', 'NMDI', 'SMI', 'SOC_SWIR', 'SOC_VIS',
                    'WIW', 'SAVI', 'NBR', 'GCI', 'ARVI', 'Chlorophyll']
valid_indices_s1 = ['RVI','VVVH_Ratio','VVVH_Diff','SMI']

# =========================
# Modèles Swagger
# =========================
model_s2_flexible = api.model('S2FlexibleRequest', {
    'uuid': fields.List(fields.List(fields.List(fields.String)), required=True, description='Id Area',default=default_uuid),
    'boundry': fields.List(fields.List(fields.List(fields.Float)), required=True, description='Liste de coordonnées', default=default_aoi),
    'date': fields.String(required=False, description='Date unique au format YYYY-MM-DD', default='2025-09-01'),
    'start_date': fields.String(required=False, description='Date de début au format YYYY-MM-DD', default='2025-09-01'),
    'end_date': fields.String(required=False, description='Date de fin au format YYYY-MM-DD', default='2025-09-09'),
})

model_s1 = api.model('S1Request', {
    'aoi': fields.List(fields.List(fields.List(fields.Float)), required=True, description='Liste de coordonnées', default=default_aoi),
    'date': fields.String(required=True, description='Date au format YYYY-MM-DD', default='2025-09-01'),
    'indices': fields.List(fields.String, required=True, description='Indices S1 à calculer', default=['RVI','VVVH_Ratio'])
})

model_generic = api.model('GenericRequest', {
    'satellite': fields.List(fields.String, required=False, description='Liste de satellites: S1 et/ou S2', default=['S1', 'S2']),
    'aoi': fields.List(fields.List(fields.List(fields.Float)), required=True, description='Liste de coordonnées', default=default_aoi),
    'date': fields.String(required=True, description='Date au format YYYY-MM-DD', default='2025-09-01')
})

# =========================
# Endpoint Sentinel-2
# =========================
@api.route('/get_indices_s2')
class Sentinel2(Resource):
    @api.expect(model_s2_flexible)
    def post(self):
        data = request.get_json()
        boundry = data.get('boundry')
        date = data.get('date')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        indices = data.get('indices')
        area_uuid = data.get('uuid')

        if not boundry or not area_uuid:
            return {"error": "Veuillez fournir 'boundry' et 'uuid'"}, 400

        # Si aucun indice fourni, on prend tous les indices valides
        if not indices:
            indices = valid_indices_s2
        else:
            # Filtrer indices valides
            indices = [i for i in indices if i in valid_indices_s2]

        # Appel à compute_indices_s2
        result = compute_indices_s2(
            boundry_coords=boundry,
            date_str=date,
            start_date=start_date,
            end_date=end_date,
            indices_list=indices,
            area_uuid=area_uuid
        )

        return result

# =========================
# Endpoint Sentinel-1
# =========================
@api.route('/get_indices_s1')
class Sentinel1(Resource):
    @api.expect(model_s1)
    def post(self):
        data = request.get_json()
        aoi = data.get('aoi')
        date = data.get('date')
        indices = data.get('indices')

        if not aoi or not date or not indices:
            return {"error": "Veuillez fournir 'aoi', 'date' et 'indices'"}, 400

        # Filtrer indices valides
        valid = [i for i in indices if i in valid_indices_s1]
        invalid = [i for i in indices if i not in valid_indices_s1]

        result = compute_indices_s1(aoi_coords=aoi, date_str=date, indices_list=valid)

        if invalid:
            result['warning'] = f"Indices non valides pour Sentinel-1 ignorés: {invalid}"
        return result

# =========================
# Endpoint générique
# =========================
@api.route('/get_indices')
class GenericIndices(Resource):
    @api.expect(model_generic)
    def post(self):
        data = request.get_json()
        satellites = data.get('satellite')
        if not satellites:
            satellites = ['S1', 'S2']
        if isinstance(satellites, str):
            satellites = [satellites]

        aoi_coords = data.get('aoi')
        date_str = data.get('date')
        if not aoi_coords or not date_str:
            return {"error": "Veuillez fournir 'aoi' et 'date'"}, 400

        response = {}
        for sat in satellites:
            if sat.upper() == "S2":
                response['S2'] = compute_indices_s2(aoi_coords=aoi_coords, date_str=date_str, indices_list=valid_indices_s2, area_id="AREA1")
            elif sat.upper() == "S1":
                response['S1'] = compute_indices_s1(aoi_coords=aoi_coords, date_str=date_str, indices_list=valid_indices_s1)
            else:
                response[sat] = {"error": "Satellite non reconnu. Choisir 'S1' ou 'S2'."}
        return response

if __name__ == '__main__':
    app.run(debug=True)
