#!/usr/bin/env python3
"""
Exemple d'utilisation de la version parallélisée optimisée.
"""

from utils import (
    compute_indices_s2_parallel, 
    get_active_tasks_status, 
    wait_for_tasks_completion
)
import time

def example_fast_processing():
    """
    Exemple 1: Traitement rapide sans attendre la completion.
    Idéal pour lancer beaucoup d'exports rapidement.
    """
    print("🚀 Exemple 1: Traitement rapide (mode fire-and-forget)")
    print("=" * 60)
    
    # Zone d'intérêt (remplacez par vos coordonnées)
    area_coords = [[[
        [2.0, 48.0],
        [2.1, 48.0], 
        [2.1, 48.1],
        [2.0, 48.1],
        [2.0, 48.0]
    ]]]
    
    # Indices à calculer
    indices = ["NDVI", "NDWI", "EVI", "NDMI", "MNDWI"]
    
    start_time = time.time()
    
    result = compute_indices_s2_parallel(
        boundry_coords=area_coords,
        date_str="2023-07-15",
        indices_list=indices,
        area_uuid="example_fast_area",
        max_workers=20,  # Parallélisation maximale
        wait_for_completion=False  # Ne pas attendre
    )
    
    processing_time = time.time() - start_time
    
    print(f"✅ Traitement terminé en {processing_time:.2f}s")
    print(f"📊 {result.get('export_tasks', 0)} exports lancés en parallèle")
    print(f"🎯 {len(result.get('frontend', []))} résultats générés")
    print("\n💡 Les exports continuent en arrière-plan sur GEE")

def example_monitored_processing():
    """
    Exemple 2: Traitement avec monitoring et attente.
    Idéal quand vous voulez connaître le statut final.
    """
    print("\n🔍 Exemple 2: Traitement avec monitoring")
    print("=" * 60)
    
    area_coords = [[[
        [1.0, 47.0],
        [1.1, 47.0],
        [1.1, 47.1], 
        [1.0, 47.1],
        [1.0, 47.0]
    ]]]
    
    indices = ["NDVI", "NDWI", "EVI"]
    
    start_time = time.time()
    
    result = compute_indices_s2_parallel(
        boundry_coords=area_coords,
        start_date="2023-06-01",
        end_date="2023-06-15",  # Période plus large
        indices_list=indices,
        area_uuid="example_monitored_area",
        max_workers=15,
        wait_for_completion=True  # Attendre la completion
    )
    
    total_time = time.time() - start_time
    
    print(f"✅ Traitement complet terminé en {total_time:.2f}s")
    
    if 'completion_status' in result:
        status = result['completion_status']
        print(f"📊 Statut final:")
        print(f"   ✅ Réussis: {status['completed']}")
        print(f"   ❌ Échecs: {status['failed']}")
        print(f"   📈 Total: {status['total']}")

def example_batch_processing():
    """
    Exemple 3: Traitement en lot de plusieurs zones.
    """
    print("\n📦 Exemple 3: Traitement en lot")
    print("=" * 60)
    
    # Plusieurs zones à traiter
    zones = [
        {
            "coords": [[[0.0, 46.0], [0.1, 46.0], [0.1, 46.1], [0.0, 46.1], [0.0, 46.0]]],
            "uuid": "zone_1",
            "date": "2023-05-15"
        },
        {
            "coords": [[[1.0, 46.0], [1.1, 46.0], [1.1, 46.1], [1.0, 46.1], [1.0, 46.0]]],
            "uuid": "zone_2", 
            "date": "2023-05-20"
        },
        {
            "coords": [[[2.0, 46.0], [2.1, 46.0], [2.1, 46.1], [2.0, 46.1], [2.0, 46.0]]],
            "uuid": "zone_3",
            "date": "2023-05-25"
        }
    ]
    
    indices = ["NDVI", "NDWI"]
    all_results = []
    
    print(f"🎯 Traitement de {len(zones)} zones en parallèle")
    
    start_time = time.time()
    
    for i, zone in enumerate(zones, 1):
        print(f"\n📍 Zone {i}/{len(zones)}: {zone['uuid']}")
        
        result = compute_indices_s2_parallel(
            boundry_coords=zone["coords"],
            date_str=zone["date"],
            indices_list=indices,
            area_uuid=zone["uuid"],
            max_workers=10,  # Moins de workers par zone
            wait_for_completion=False
        )
        
        all_results.append(result)
        print(f"   ✅ {result.get('export_tasks', 0)} exports lancés")
    
    batch_time = time.time() - start_time
    total_exports = sum(r.get('export_tasks', 0) for r in all_results)
    
    print(f"\n🎉 Traitement en lot terminé en {batch_time:.2f}s")
    print(f"📊 Total: {total_exports} exports lancés pour {len(zones)} zones")
    print(f"⚡ Vitesse: {total_exports/batch_time:.1f} exports/seconde")

def example_optimized_workflow():
    """
    Exemple 4: Workflow optimisé avec toutes les bonnes pratiques.
    """
    print("\n⚡ Exemple 4: Workflow optimisé")
    print("=" * 60)
    
    # Configuration optimisée
    config = {
        "area_coords": [[[3.0, 45.0], [3.2, 45.0], [3.2, 45.2], [3.0, 45.2], [3.0, 45.0]]],
        "start_date": "2023-08-01",
        "end_date": "2023-08-31",
        "indices": ["NDVI", "NDWI", "EVI", "NDMI", "MNDWI", "NDRE"],
        "area_uuid": "optimized_workflow_area",
        "max_workers": 25  # Parallélisation maximale
    }
    
    print("🔧 Configuration:")
    print(f"   📅 Période: {config['start_date']} → {config['end_date']}")
    print(f"   📊 Indices: {len(config['indices'])} indices")
    print(f"   🔧 Workers: {config['max_workers']}")
    
    start_time = time.time()
    
    # Lancement rapide
    result = compute_indices_s2_parallel(
        boundry_coords=config["area_coords"],
        start_date=config["start_date"],
        end_date=config["end_date"],
        indices_list=config["indices"],
        area_uuid=config["area_uuid"],
        max_workers=config["max_workers"],
        wait_for_completion=False
    )
    
    launch_time = time.time() - start_time
    
    print(f"\n🚀 Lancement terminé en {launch_time:.2f}s")
    print(f"📊 {result.get('export_tasks', 0)} exports lancés")
    print(f"🎯 {len(result.get('frontend', []))} résultats disponibles")
    
    # Avantages de cette approche
    print(f"\n💡 Avantages de cette approche:")
    print(f"   ⚡ Gain de temps énorme vs version séquentielle")
    print(f"   🔄 Tous les exports démarrent immédiatement")
    print(f"   📈 GEE gère la mise en file automatiquement")
    print(f"   🎯 Pas de blocage du script Python")
    print(f"   📊 Monitoring optionnel disponible")

if __name__ == "__main__":
    print("🎯 Exemples d'utilisation de la parallélisation GEE")
    print("=" * 70)
    
    # Exécuter tous les exemples
    example_fast_processing()
    example_monitored_processing() 
    example_batch_processing()
    example_optimized_workflow()
    
    print("\n" + "=" * 70)
    print("🎉 Tous les exemples terminés!")
    print("\n💡 Points clés à retenir:")
    print("   • Utilisez wait_for_completion=False pour un maximum de vitesse")
    print("   • Ajustez max_workers selon votre quota GEE (10-25 recommandé)")
    print("   • La vérification batch des fichiers évite les exports inutiles")
    print("   • GEE gère automatiquement la mise en file des tâches")
