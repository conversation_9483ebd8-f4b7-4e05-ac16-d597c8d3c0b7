#!/usr/bin/env python3
"""
Démonstration simple de la parallélisation optimisée.
"""

from utils import compute_indices_s2_parallel
import time

def demo_simple():
    """
    Démonstration simple avec une petite zone de test.
    """
    print("🚀 Démonstration de la Parallélisation GEE")
    print("=" * 50)
    
    # Zone de test (petite zone en France)
    coords = [[[
        [2.0, 48.0],   # Coin sud-ouest
        [2.05, 48.0],  # Coin sud-est  
        [2.05, 48.05], # Coin nord-est
        [2.0, 48.05],  # Coin nord-ouest
        [2.0, 48.0]    # Fermeture du polygone
    ]]]
    
    # Configuration
    config = {
        "boundry_coords": coords,
        "date_str": "2023-07-15",
        "indices_list": ["NDVI", "NDWI"],  # Seulement 2 indices pour la démo
        "area_uuid": "demo_parallel_zone",
        "max_workers": 10,
        "wait_for_completion": False  # Mode rapide
    }
    
    print("📋 Configuration de la démo:")
    print(f"   📅 Date: {config['date_str']}")
    print(f"   📊 Indices: {config['indices_list']}")
    print(f"   🔧 Workers: {config['max_workers']}")
    print(f"   ⚡ Mode: {'Rapide (sans attente)' if not config['wait_for_completion'] else 'Avec attente'}")
    
    print(f"\n🎯 Lancement du traitement parallélisé...")
    start_time = time.time()
    
    try:
        result = compute_indices_s2_parallel(**config)
        
        processing_time = time.time() - start_time
        
        print(f"\n✅ Traitement terminé en {processing_time:.2f} secondes")
        print(f"📊 Statistiques:")
        print(f"   🚀 Exports lancés: {result.get('export_tasks', 0)}")
        print(f"   📈 Résultats frontend: {len(result.get('frontend', []))}")
        print(f"   🔧 Résultats backend: {len(result.get('backend', []))}")
        print(f"   ⚡ Mode parallèle: {result.get('parallel_mode', False)}")
        
        if result.get('frontend'):
            print(f"\n📋 Aperçu des résultats:")
            for i, item in enumerate(result['frontend'][:2], 1):  # Afficher les 2 premiers
                print(f"   {i}. {item['index']} - {item['capture_date']}")
                print(f"      Raw: {item['data']['raw']}")
                print(f"      Color: {item['data']['color']}")
        
        print(f"\n💡 Avantages de cette approche:")
        print(f"   ⚡ Tous les exports démarrent immédiatement")
        print(f"   🔄 Pas d'attente séquentielle")
        print(f"   📈 GEE gère la mise en file automatiquement")
        print(f"   🎯 Script Python non bloqué")
        
    except Exception as e:
        print(f"❌ Erreur lors du traitement: {e}")
        print(f"💡 Vérifiez vos credentials GEE et la connectivité")

if __name__ == "__main__":
    demo_simple()
    
    print(f"\n" + "=" * 50)
    print(f"🎉 Démonstration terminée!")
    print(f"\n📚 Pour aller plus loin:")
    print(f"   • Consultez README_PARALLEL.md pour la documentation complète")
    print(f"   • Exécutez example_parallel_usage.py pour plus d'exemples")
    print(f"   • Lancez test_parallel_performance.py pour les tests de performance")
