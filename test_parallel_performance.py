#!/usr/bin/env python3
"""
Script de test pour valider les améliorations de performance de la parallélisation.
"""

import time
from utils import compute_indices_s2, compute_indices_s2_parallel, get_active_tasks_status

def test_performance_comparison():
    """
    Compare les performances entre la version originale et parallélisée.
    """
    
    # Zone de test (exemple de coordonnées)
    test_coords = [[[
        [2.3522, 48.8566],  # Paris - exemple
        [2.3622, 48.8566],
        [2.3622, 48.8666],
        [2.3522, 48.8666],
        [2.3522, 48.8566]
    ]]]
    
    # Paramètres de test
    test_date = "2023-06-15"
    test_indices = ["NDVI", "NDWI", "EVI"]
    test_area_uuid = "test_parallel_performance"
    
    print("🧪 Test de performance - Parallélisation GEE")
    print("=" * 50)
    
    # Test 1: Version parallélisée (sans attendre la completion)
    print("\n1️⃣ Test version parallélisée (mode rapide)")
    start_time = time.time()
    
    try:
        result_parallel = compute_indices_s2_parallel(
            boundry_coords=test_coords,
            date_str=test_date,
            indices_list=test_indices,
            area_uuid=test_area_uuid,
            max_workers=10,
            wait_for_completion=False  # Mode rapide
        )
        
        parallel_time = time.time() - start_time
        print(f"✅ Version parallélisée terminée en {parallel_time:.2f}s")
        print(f"   📊 {result_parallel.get('export_tasks', 0)} exports lancés")
        
    except Exception as e:
        print(f"❌ Erreur version parallélisée: {e}")
        return
    
    # Test 2: Version parallélisée (avec attente limitée)
    print("\n2️⃣ Test version parallélisée (avec suivi)")
    start_time = time.time()
    
    try:
        result_parallel_wait = compute_indices_s2_parallel(
            boundry_coords=test_coords,
            date_str=test_date,
            indices_list=test_indices,
            area_uuid=f"{test_area_uuid}_wait",
            max_workers=15,
            wait_for_completion=True  # Avec attente
        )
        
        parallel_wait_time = time.time() - start_time
        print(f"✅ Version parallélisée avec attente terminée en {parallel_wait_time:.2f}s")
        
        if 'completion_status' in result_parallel_wait:
            status = result_parallel_wait['completion_status']
            print(f"   📊 Statut final: {status['completed']} réussis, {status['failed']} échecs sur {status['total']}")
        
    except Exception as e:
        print(f"❌ Erreur version parallélisée avec attente: {e}")
    
    # Test 3: Comparaison avec version originale (optionnel - peut être long)
    print("\n3️⃣ Test version originale (pour comparaison)")
    print("⚠️  Ce test peut être long - commentez si nécessaire")
    
    # Décommentez pour tester la version originale
    # start_time = time.time()
    # try:
    #     result_original = compute_indices_s2(
    #         boundry_coords=test_coords,
    #         date_str=test_date,
    #         indices_list=test_indices,
    #         area_uuid=f"{test_area_uuid}_original"
    #     )
    #     
    #     original_time = time.time() - start_time
    #     print(f"✅ Version originale terminée en {original_time:.2f}s")
    #     
    #     # Calcul du gain de performance
    #     if parallel_time > 0:
    #         speedup = original_time / parallel_time
    #         print(f"🚀 Accélération: {speedup:.1f}x plus rapide")
    #     
    # except Exception as e:
    #     print(f"❌ Erreur version originale: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Tests de performance terminés")

def test_batch_file_check():
    """
    Test la vérification batch des fichiers.
    """
    from utils import batch_file_exists, BUCKET_NAME
    
    print("\n🔍 Test de vérification batch des fichiers")
    
    # Fichiers de test (certains existent, d'autres non)
    test_files = [
        "test/file1.tif",
        "test/file2.tif", 
        "test/file3.tif",
        "nonexistent/file.tif"
    ]
    
    start_time = time.time()
    results = batch_file_exists(BUCKET_NAME, test_files, max_workers=5)
    batch_time = time.time() - start_time
    
    print(f"⚡ Vérification batch de {len(test_files)} fichiers en {batch_time:.2f}s")
    for file_path, exists in results.items():
        status = "✅ Existe" if exists else "❌ N'existe pas"
        print(f"   {file_path}: {status}")

def test_task_monitoring():
    """
    Test le système de monitoring des tâches.
    """
    print("\n📊 Test du monitoring des tâches")
    
    # Simulation de tâches (remplacez par de vraies tâches si disponibles)
    fake_tasks = [
        ("path1.tif", None),  # Tâche None (fichier existant)
        ("path2.tif", None),  # Tâche None (fichier existant)
    ]
    
    status = get_active_tasks_status(fake_tasks)
    print(f"📈 Statut des tâches: {status}")

if __name__ == "__main__":
    print("🚀 Démarrage des tests de performance")
    
    # Test principal
    test_performance_comparison()
    
    # Tests additionnels
    test_batch_file_check()
    test_task_monitoring()
    
    print("\n🎉 Tous les tests terminés!")
