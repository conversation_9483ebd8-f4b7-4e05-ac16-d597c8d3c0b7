import ee , json
from datetime import datetime, timezone
import os
from dotenv import load_dotenv
from google.cloud import storage as gcs
import time
from matplotlib import colormaps
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from typing import List, Tuple, Dict, Optional

# Charger le .env
load_dotenv()

# Récupérer les variables
SERVICE_ACCOUNT = os.getenv('GEE_SERVICE_ACCOUNT')
KEY_PATH = os.getenv('GEE_PRIVATE_KEY_PATH')

# Initialiser Earth Engine
credentials = ee.ServiceAccountCredentials(SERVICE_ACCOUNT, KEY_PATH)
ee.Initialize(credentials)

BUCKET_NAME = "seabex-satellite-imagery"
COLOR_MAP = {
    "red": [0, 0, 255],  # BGR
    "green": [0, 255, 0],  # BGR
    "white": [255, 255, 255],  # BGR
}


# -------------------------
# Configuration pour chaque indice
# -------------------------
index_palettes = {
    "LAI": {"min": 0, "max": 10, "colormap": "YlGn"},
    "SMI": {"min": 0, "max": 1, "colormap": "BrBG"},
    "MNDWI": {"min": -1, "max": 1, "colormap": "Blues"},
    "NDMI": {"min": -1, "max": 1, "colormap": "BrBG"},
    "FAPAR": {"min": 0, "max": 1, "colormap": "YlGn"},
    "CCC": {"min": 0, "max": 1, "colormap": "YlGn"},
    "CWC": {"min": 0, "max": 1, "colormap": "coolwarm_r"},
    "IRECI": {"min": -1, "max": 1, "colormap": "RdYlGn"},
    "MSAVI2": {"min": -1, "max": 1, "colormap": "YlGn"},
    "NMDI": {"min": -1, "max": 1, "colormap": "BrBG"},
    "WIW": {"min": 0, "max": 1, "colormap": "coolwarm_r"},
    "SOC_VIS": {"min": 0, "max": 1, "colormap": "copper"},
    "SOC_SWIR": {"min": 0, "max": 1, "colormap": "copper"},
    "NDVI": {"min": -1, "max": 1, "colormap": "RdYlGn"},
    "NDWI": {"min": -1, "max": 1, "colormap": "Blues"},
    "EVI": {"min": -1, "max": 1, "colormap": "RdYlGn"},
    "NDRE": {"min": -1, "max": 1, "colormap": "RdYlGn"},
    "ARI1": {"min": 0, "max": 0.2, "colormap": "magma"},
    "CAR_RE": {"min": 0, "max": 1, "colormap": "YlGnBu"},
    "CL_RE": {"min": 0, "max": 1, "colormap": "YlGn"},
    "NDREX": {"min": -1, "max": 1, "colormap": "RdYlGn"},
    "NMDI_SOIL": {"min": -1, "max": 1, "colormap": "BrBG"},
    "NMDI_VEG": {"min": -1, "max": 1, "colormap": "YlGn"},
    "SI": {"min": 0, "max": 1, "colormap": "YlGn"},
    "YRSI": {"min": 0, "max": 1, "colormap": "RdYlGn"},
    "Chlorophyll": {"min": -1, "max": 5, "colormap": "Greens"},
}

index_palettes["DEFAULT"] = {
    "min": -1,
    "max": 1,
    "negativeColor": "red",
    "positiveColor": "green",
    "stableColor": "white"
}
# -------------------------
# Fonctions utilitaires
# -------------------------

# -------------------------
# Fonctions de parallélisation optimisées
# -------------------------

def batch_file_exists(bucket_name: str, blob_paths: List[str], max_workers: int = 10) -> Dict[str, bool]:
    """
    Vérifie l'existence de plusieurs fichiers en parallèle dans GCS.

    Args:
        bucket_name: Nom du bucket GCS
        blob_paths: Liste des chemins des fichiers à vérifier
        max_workers: Nombre maximum de threads parallèles

    Returns:
        Dictionnaire {chemin: existe}
    """
    def check_single_file(blob_path: str) -> Tuple[str, bool]:
        try:
            client = gcs.Client.from_service_account_json(KEY_PATH)
            bucket = client.bucket(bucket_name)
            blob = bucket.blob(blob_path)
            return blob_path, blob.exists()
        except Exception as e:
            print(f"❌ Erreur vérification GCS pour {blob_path}: {e}")
            return blob_path, False

    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_path = {executor.submit(check_single_file, path): path for path in blob_paths}
        for future in as_completed(future_to_path):
            path, exists = future.result()
            results[path] = exists

    return results

def launch_export_task(image, variant: str, index_name: str, area_uuid: str,
                      capture_date: str, boundry, bucket_name: str = BUCKET_NAME) -> Tuple[str, Optional[object]]:
    """
    Lance une tâche d'export de manière non-bloquante.

    Returns:
        Tuple (gcs_path, task) où task peut être None si le fichier existe déjà
    """
    try:
        file_name = f"{capture_date}_{variant}_{area_uuid}.tif"
        folder_path = f"{area_uuid}/{index_name}"
        gcs_path = f"{folder_path}/{file_name}"

        # Création de la tâche d'export (sans vérification préalable pour optimiser)
        task = ee.batch.Export.image.toCloudStorage(
            image=image,
            description=f"export_{variant}_{index_name}_{capture_date}",
            bucket=bucket_name,
            fileNamePrefix=gcs_path.replace(".tif", ""),
            region=boundry,
            scale=10,
            fileFormat="GeoTIFF",
            maxPixels=1e13
        )
        task.start()
        print(f"🚀 Export lancé : {variant} {index_name} {capture_date}")

        return gcs_path, task

    except Exception as e:
        print(f"❌ Erreur export {variant} {index_name}: {e}")
        return gcs_path, None

def launch_band_export_task(image, band: str, area_uuid: str, capture_date: str,
                           boundry, scale: int, bucket_name: str = BUCKET_NAME) -> Tuple[str, Optional[object]]:
    """
    Lance une tâche d'export de bande de manière non-bloquante.
    """
    try:
        file_name = f"{capture_date}_{band.lower()}_{area_uuid}.tif"
        folder_path = f"{area_uuid}/bandes"
        gcs_path = f"{folder_path}/{file_name}"

        band_img = image.select(band)
        task = ee.batch.Export.image.toCloudStorage(
            image=band_img,
            description=f"export_band_{band}_{capture_date}",
            bucket=bucket_name,
            fileNamePrefix=gcs_path.replace(".tif", ""),
            region=boundry,
            scale=scale,
            fileFormat="GeoTIFF",
            maxPixels=1e13
        )
        task.start()
        print(f"🚀 Export bande lancé : {band} (résolution {scale}m)")

        return gcs_path, task

    except Exception as e:
        print(f"❌ Erreur export bande {band}: {e}")
        return gcs_path, None
def bgr_to_hex(bgr):
    b, g, r = bgr
    return f'#{r:02X}{g:02X}{b:02X}'

def export_bands_original(image, capture_date, area_uuid, boundry, bucket_name=BUCKET_NAME):
    """
    Exporte les bandes originales Sentinel-2 avec leur résolution native
    (10m, 20m ou 60m selon la bande) seulement si elles n’existent pas déjà.
    """
    band_resolutions = {
        "B1": 60, "B9": 60,
        "B2": 10, "B3": 10, "B4": 10, "B8": 10,
        "B5": 20, "B6": 20, "B7": 20, "B8A": 20, "B11": 20, "B12": 20,
        "AOT": 10, "WVP": 10, "SCL": 20
    }

    band_names = image.bandNames().getInfo()
    tasks = []

    for band in band_names:
        scale = band_resolutions.get(band, 10)  # fallback 10m si non listée
        file_name = f"{capture_date}_{band.lower()}_{area_uuid}.tif"
        folder_path = f"{area_uuid}/bandes"
        gcs_path = f"{folder_path}/{file_name}"

        # Vérification AVANT l'export
        if file_exists(bucket_name, gcs_path):
            print(f"⚠️ Bande déjà présente : {gcs_path}")
            continue

        # Si absente → lancer l’export
        band_img = image.select(band)  # ⚠️ pas de normalisation ici
        task = ee.batch.Export.image.toCloudStorage(
            image=band_img,
            description=f"export_band_{band}_{capture_date}",
            bucket=bucket_name,
            fileNamePrefix=gcs_path.replace(".tif", ""),
            region=boundry,
            scale=scale,
            fileFormat="GeoTIFF",
            maxPixels=1e13
        )
        task.start()
        print(f"➡️ Export bande {band} lancé (résolution {scale}m) : https://storage.googleapis.com/{bucket_name}/{gcs_path}")
        tasks.append((task, gcs_path))

    return tasks

# -------------------------
# Génération d'une image colorisée dynamique
# -------------------------
def colorize_index(ind_img, ind_name, stats=None, n_colors=256):
    """
    Colorise dynamiquement un ee.Image selon l'indice et une colormap Matplotlib.
    """
    cfg = index_palettes.get(ind_name, index_palettes["DEFAULT"])

    # Valeurs min/max dynamiques
    min_val = stats.get(f"{ind_name}_min") if stats and stats.get(f"{ind_name}_min") is not None else cfg.get("min", 0)
    max_val = stats.get(f"{ind_name}_max") if stats and stats.get(f"{ind_name}_max") is not None else cfg.get("max", 1)

    if max_val == min_val:
        max_val = min_val + 1e-6  # éviter division par zéro

    if "colormap" in cfg:
        # Récupère la colormap Matplotlib
        cmap_name = cfg["colormap"]
        cmap = colormaps[cmap_name]

        # Génère une palette hex dynamique (format accepté par EE)
        palette_rgb = (cmap(np.linspace(0, 1, n_colors))[:, :3] * 255).astype(int)
        palette_hex = [f"#{r:02X}{g:02X}{b:02X}" for r, g, b in palette_rgb]

        # Appliquer la palette à l'image EE
        colored_img = ind_img.visualize(min=min_val, max=max_val, palette=palette_hex)
    else:
        # fallback si pas de colormap
        neg = bgr_to_hex(COLOR_MAP.get(cfg.get("negativeColor", "red"), [0, 0, 255]))
        stable = bgr_to_hex(COLOR_MAP.get(cfg.get("stableColor", "white"), [255, 255, 255]))
        pos = bgr_to_hex(COLOR_MAP.get(cfg.get("positiveColor", "green"), [0, 255, 0]))
        colored_img = ind_img.visualize(min=min_val, max=max_val, palette=[neg, stable, pos])

    return colored_img


# -------------------------
# Export GEE (parallèle) corrigé
# -------------------------
def export_index_variant(image, index_name, variant, area_uuid, capture_date, boundry,
                         bucket_name=BUCKET_NAME):
    """
    Exporte une image Earth Engine vers Google Cloud Storage (GeoTIFF).
    Ne bloque pas l'exécution : la tâche est renvoyée pour suivi parallèle.

    Returns:
        gcs_path (str): chemin complet du fichier dans le bucket
        task (ee.batch.Task | None): tâche GEE (None si le fichier existe déjà)
    """
    try:
        #year = datetime.strptime(capture_date, "%Y-%m-%d").year
        file_name = f"{capture_date}_{variant}_{area_uuid}.tif"
        folder_path = f"{area_uuid}/{index_name}"
        gcs_path = f"{folder_path}/{file_name}"

        # Vérification si le fichier existe déjà
        if file_exists(bucket_name, gcs_path):
            print(f"⚠️ Déjà présent : https://storage.googleapis.com/{bucket_name}/{gcs_path}")
            return gcs_path, None  # task = None

        # Création de la tâche d'export
        task = ee.batch.Export.image.toCloudStorage(
            image=image,
            description=f"export_{variant}_{index_name}_{capture_date}",
            bucket=bucket_name,
            fileNamePrefix=gcs_path.replace(".tif", ""),
            region=boundry,
            scale=10,
            fileFormat="GeoTIFF",
            maxPixels=1e13
        )
        task.start()
        print(f"➡️ Export lancé : https://storage.googleapis.com/{bucket_name}/{gcs_path}")

        # Ne pas bloquer ici : on renvoie la tâche pour suivi
        return gcs_path, task

    except Exception as e:
        print(f"❌ Erreur export : {e}")
        return gcs_path, None



# Vérifier si un fichier existe déjà dans GCS
def file_exists(bucket_name, blob_path):
    """
    Vérifie si un fichier existe déjà dans le bucket GCS.
    """
    try:
        client = gcs.Client.from_service_account_json(KEY_PATH)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_path)
        return blob.exists()
    except Exception as e:
        print(f"❌ Erreur vérification GCS: {e}")
        return False


def load_statistics(area_uuid, index, capture_date, bucket_name):
    """
    Charge une statistique précise depuis results/<area_uuid>.json
    Retourne None si le fichier ou l'entrée n'existent pas.
    """
    try:
        client = gcs.Client.from_service_account_json(KEY_PATH)
        blob_path = f"results/{area_uuid}.json"
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_path)

        if not blob.exists():
            return None

        stats_data = json.loads(blob.download_as_text())
        return stats_data.get(index, {}).get(capture_date, None)

    except Exception as e:
        print(f"❌ Erreur load_statistics : {e}")
        return None


def save_statistics(area_uuid, index, capture_date, statistics, bucket_name):
    """
    Ajoute des stats dans results/<area_uuid>.json sans supprimer les données existantes.
    - Si le fichier existe, on le charge et on fusionne les données
    - On ajoute la nouvelle entrée
    - On réécrit l'objet avec le JSON mis à jour
    """
    try:
        client = gcs.Client.from_service_account_json(KEY_PATH)
        blob_path = f"results/{area_uuid}.json"
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_path)

        stats_data = {}

        # Charger le JSON existant si le fichier est déjà présent
        if blob.exists():
            stats_data = json.loads(blob.download_as_text())

        # Ajouter / mettre à jour l'entrée
        if index not in stats_data:
            stats_data[index] = {}

        stats_data[index][capture_date] = statistics

        # Réécrire le fichier complet avec les nouvelles données
        blob.upload_from_string(
            json.dumps(stats_data, indent=2, ensure_ascii=False),
            content_type="application/json"
        )

        print(f"📊 Stats ajoutées dans gs://{bucket_name}/{blob_path}")

    except Exception as e:
        print(f"❌ Erreur save_statistics : {e}")

# -------------------------
# Fonction principale
# -------------------------
def compute_indices_s2(boundry_coords, date_str=None, start_date=None, end_date=None, indices_list=None, area_uuid=None):
    """
    Calcule les indices Sentinel-2 pour la zone fournie et :
      - vérifie si les tifs raw/color existent déjà dans le bucket (skip si les deux existent),
      - pour les manquants, calcule l'indice, génère la version colorée et lance les exports en parallèle,
      - suit les tasks jusqu'à leur fin et retourne frontend + backend results.

    Params:
      - boundry_coords : polygon coords (liste)
      - date_str OR (start_date et end_date)
      - indices_list : liste de noms d'indices (ex: ["NDVI","NDWI",...])
      - area_uuid : identifiant unique de la zone
    """
    if indices_list is None:
        indices_list = []

    boundry = ee.Geometry.Polygon(boundry_coords)

    # Période
    if date_str:
        start = ee.Date(date_str)
        end = start.advance(2, 'day')
    elif start_date and end_date:
        start = ee.Date(start_date)
        end = ee.Date(end_date)
    else:
        return {"error": "Veuillez fournir 'date_str' ou 'start_date' et 'end_date'."}

    # Collections Sentinel-2
    s2Sr = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED').filterDate(start, end).filterBounds(boundry)
    s2Clouds = ee.ImageCollection('COPERNICUS/S2_CLOUD_PROBABILITY').filterDate(start, end).filterBounds(boundry)

    joined = ee.Join.saveFirst('cloud_mask').apply(
        s2Sr, s2Clouds, ee.Filter.equals(leftField='system:index', rightField='system:index')
    )

    def mask_clouds(img):
        cloud_prob = ee.Image(img.get('cloud_mask')).select('probability')
        mask = cloud_prob.lt(30)
        return img.updateMask(mask).divide(10000).copyProperties(img, ["system:time_start", "CLOUDY_PIXEL_PERCENTAGE"])

    collection = ee.ImageCollection(joined).map(mask_clouds)
    if collection.size().getInfo() == 0:
        return {"error": "Aucune image disponible dans cette période."}

    # Garder la meilleure image par jour
    def first_per_day(col):
        days = col.aggregate_array('system:time_start').map(lambda t: ee.Date(t).format('YYYY-MM-dd'))
        unique_days = ee.List(days).distinct()

        def get_best(day):
            day = ee.String(day)
            day_start = ee.Date(day)
            day_end = day_start.advance(1, 'day')
            return col.filterDate(day_start, day_end).sort('CLOUDY_PIXEL_PERCENTAGE').first()

        return ee.ImageCollection(unique_days.map(get_best)).filter(ee.Filter.notNull(['system:time_start']))

    collection = first_per_day(collection)
    imgs_list = collection.toList(collection.size())

    frontend_results = []
    backend_results = []
    all_tasks = []  # (task, gcs_path)

    # fonction pour calculer l'indice demandé
    def add_index(img, ind):
        if ind == "NDVI":
            return img.normalizedDifference(['B8', 'B4']).rename('NDVI')
        elif ind == "EVI":
            return img.expression(
                '2.5 * ((NIR - RED) / (NIR + 6*RED - 7.5*BLUE + 1))',
                {'NIR': img.select('B8'), 'RED': img.select('B4'), 'BLUE': img.select('B2')}
            ).rename('EVI')
        elif ind == "NDWI":
            return img.normalizedDifference(['B3', 'B8']).rename('NDWI')
        elif ind == "NDMI":
            return img.normalizedDifference(['B8', 'B11']).rename('NDMI')
        elif ind == "MNDWI":
            return img.normalizedDifference(['B3', 'B11']).rename('MNDWI')
        elif ind == "NDRE":
            return img.normalizedDifference(['B8', 'B5']).rename('NDRE')
        elif ind == "NDREX":
            return img.normalizedDifference(['B8A', 'B5']).rename('NDREX')
        elif ind == "CCC":
            return img.expression('(B3 - B4) / (B3 + B4)', {'B3': img.select('B3'), 'B4': img.select('B4')}).rename('CCC')
        elif ind == "CWC":
            return img.expression('B3 / B2', {'B3': img.select('B3'), 'B2': img.select('B2')}).rename('CWC')
        elif ind == "FAPAR":
            return img.expression('(B8 - B4)/(B8 + B4)', {'B8': img.select('B8'), 'B4': img.select('B4')}).rename('FAPAR')
        elif ind == "IRECI":
            return img.expression('(B8 - B4) / (B8 + B4 - B2)', {'B8': img.select('B8'), 'B4': img.select('B4'), 'B2': img.select('B2')}).rename('IRECI')
        elif ind == "LAI":
            return img.expression('3.618 * ((B8/B4) - 0.118)', {'B8': img.select('B8'), 'B4': img.select('B4')}).rename('LAI')
        elif ind == "MSAVI2":
            return img.expression('0.5 * (2*B8 + 1 - sqrt((2*B8 + 1)**2 - 8*(B8 - B4)))', {'B8': img.select('B8'), 'B4': img.select('B4')}).rename('MSAVI2')
        elif ind == "NMDI":
            return img.expression('(B8 - (B11 - B12)) / (B8 + (B11 - B12))', {'B8': img.select('B8'), 'B11': img.select('B11'), 'B12': img.select('B12')}).rename('NMDI')
        elif ind == "SMI":
            return img.expression('(B8 - B11)/(B8 + B11)', {'B8': img.select('B8'), 'B11': img.select('B11')}).rename('SMI')
        elif ind == "SOC_SWIR":
            return img.expression('(B12 - B8)/(B12 + B8)', {'B12': img.select('B12'), 'B8': img.select('B8')}).rename('SOC_SWIR')
        elif ind == "SOC_VIS":
            return img.expression('(B4 - B3)/(B4 + B3)', {'B4': img.select('B4'), 'B3': img.select('B3')}).rename('SOC_VIS')
        elif ind == "WIW":
            return img.expression('(B11 - B12)/(B11 + B12)', {'B11': img.select('B11'), 'B12': img.select('B12')}).rename('WIW')
        elif ind == "SAVI":
            return img.expression('((NIR - RED) / (NIR + RED + 0.5)) * 1.5', {'NIR': img.select('B8'), 'RED': img.select('B4')}).rename('SAVI')
        elif ind == "NBR":
            return img.normalizedDifference(['B8', 'B12']).rename('NBR')
        elif ind == "GCI":
            return img.expression('(NIR / GREEN) - 1', {'NIR': img.select('B8'), 'GREEN': img.select('B3')}).rename('GCI')
        elif ind == "ARVI":
            return img.expression('(NIR - (2*RED - BLUE)) / (NIR + (2*RED - BLUE))', {'NIR': img.select('B8'), 'RED': img.select('B4'), 'BLUE': img.select('B2')}).rename('ARVI')
        elif ind == "Chlorophyll":
            return img.expression('(NIR / RE1) - 1', {'NIR': img.select('B8'), 'RE1': img.select('B5')}).rename('Chlorophyll')
        else:
            return None

    n_images = collection.size().getInfo()
    for i in range(n_images):
        image = ee.Image(imgs_list.get(i)).clip(boundry)
        timestamp = image.get("system:time_start").getInfo()
        capture_date = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc).strftime(
            "%Y-%m-%d") if timestamp else "Date inconnue"
        # ✅ Vérifier & exporter les bandes originales
        band_tasks = export_bands_original(image, capture_date, area_uuid, boundry, BUCKET_NAME)
        all_tasks.extend(band_tasks)
        for ind in indices_list:
            # vérifier si raw et color existent déjà
            folder_path = f"{area_uuid}/{ind}"
            raw_blob = f"{folder_path}/{capture_date}_raw_{area_uuid}.tif"
            color_blob = f"{folder_path}/{capture_date}_color_{area_uuid}.tif"

            raw_exists = file_exists(BUCKET_NAME, raw_blob)
            color_exists = file_exists(BUCKET_NAME, color_blob)

            # ✅ Charger l'image indice
            ind_img = add_index(image, ind)
            if ind_img is None:
                print(f"⚠️ Indice non supporté : {ind}")
                continue

            # ✅ Charger ou calculer les stats
            cached_stats = load_statistics(area_uuid, ind, capture_date, BUCKET_NAME)
            if cached_stats:
                print(f"⚡ Stats déjà présentes dans {area_uuid}.json pour {ind} {capture_date}")
                statistics = cached_stats
            else:
                try:
                    stats = ind_img.reduceRegion(
                        reducer=ee.Reducer.percentile([0, 10, 25, 50, 75, 90, 100], None)
                        .combine(ee.Reducer.mean(), sharedInputs=True)
                        .combine(ee.Reducer.stdDev(), sharedInputs=True)
                        .combine(ee.Reducer.minMax(), sharedInputs=True),
                        geometry=boundry,
                        scale=10,
                        bestEffort=True
                    ).getInfo()
                except Exception as e:
                    stats = None
                    print(f"⚠️ Erreur reduceRegion pour {ind} {capture_date}: {e}")

                statistics = {
                    "min": stats.get(f"{ind}_min") if stats else None,
                    "max": stats.get(f"{ind}_max") if stats else None,
                    "median": stats.get(f"{ind}_p50") if stats else None,
                    "mean": stats.get(f"{ind}_mean") if stats else None,
                    "sd": stats.get(f"{ind}_stdDev") if stats else None,
                    "percentiles": {
                        "0.0": stats.get(f"{ind}_p0") if stats else None,
                        "10.0": stats.get(f"{ind}_p10") if stats else None,
                        "25.0": stats.get(f"{ind}_p25") if stats else None,
                        "50.0": stats.get(f"{ind}_p50") if stats else None,
                        "75.0": stats.get(f"{ind}_p75") if stats else None,
                        "90.0": stats.get(f"{ind}_p90") if stats else None,
                        "100.0": stats.get(f"{ind}_p100") if stats else None,
                    }
                }

                save_statistics(area_uuid, ind, capture_date, statistics, BUCKET_NAME)
            # ✅ Si déjà présent → pas d'export
            if raw_exists and color_exists:
                print(f"⚠️ Déjà présent : {ind} {capture_date}")
                raw_url = f"https://storage.googleapis.com/{BUCKET_NAME}/{raw_blob}"
                color_url = f"https://storage.googleapis.com/{BUCKET_NAME}/{color_blob}"
            else:
                # Palette colorisée
                colored_img = colorize_index(ind_img, ind, statistics)

                if not raw_exists:
                    raw_path, raw_task = export_index_variant(ind_img, ind, "raw", area_uuid, capture_date, boundry,
                                                              BUCKET_NAME)
                    if raw_task:
                        all_tasks.append((raw_task, raw_path))
                else:
                    raw_path = raw_blob

                if not color_exists:
                    color_path, color_task = export_index_variant(colored_img, ind, "color", area_uuid, capture_date,
                                                                  boundry, BUCKET_NAME)
                    if color_task:
                        all_tasks.append((color_task, color_path))
                else:
                    color_path = color_blob

                raw_url = f"https://storage.googleapis.com/{BUCKET_NAME}/{raw_path}" if raw_path else None
                color_url = f"https://storage.googleapis.com/{BUCKET_NAME}/{color_path}" if color_path else None

            # ✅ Résultats
            frontend_results.append({
                "bounds": boundry_coords[0],
                "capture_date": capture_date,
                "data": {
                    "metadata": {"observation_date": capture_date},
                    "raw": raw_url,
                    "color": color_url,
                    "png": color_url,
                    "tiles_color": "NOT_IMPLEMENTED",
                    "tiles_demo": "NOT_IMPLEMENTED",
                    "shp": "NOT_IMPLEMENTED",
                    "geojson": "NOT_IMPLEMENTED",
                    "statistics": "NOT_IMPLEMENTED",
                    "frequencies": "NOT_IMPLEMENTED"
                },
                "statistics": statistics,
                "index": ind,
                "product": "Sentinel-2"
            })

            backend_results.append({
                ind: {
                    "capture_date": capture_date,
                    "raw": raw_url,
                    "color": color_url,
                    "area_uuid": area_uuid,
                    "boundary": boundry_coords[0]
                }
            })

    # Suivi des exports
    if all_tasks:
        print("⏳ Suivi des exports en parallèle...")
        while any(t[0].active() for t in all_tasks):
            for task, path in all_tasks:
                if task.active():
                    print(f"   ➡️ En cours : https://storage.googleapis.com/{BUCKET_NAME}/{path}")
            time.sleep(15)

        for task, path in all_tasks:
            status = task.status()
            if status.get('state') == 'COMPLETED':
                print(f"✅ Terminé : https://storage.googleapis.com/{BUCKET_NAME}/{path}")
            else:
                print(f"❌ Échec pour https://storage.googleapis.com/{BUCKET_NAME}/{path} : {status}")

    return {"frontend": frontend_results, "backend": backend_results}


# -------------------------
# Version parallélisée optimisée
# -------------------------

def compute_indices_s2_parallel(boundry_coords, date_str=None, start_date=None, end_date=None,
                               indices_list=None, area_uuid=None, max_workers=20, wait_for_completion=False):
    """
    Version parallélisée optimisée de compute_indices_s2.

    Améliorations:
    - Lance tous les exports en parallèle immédiatement
    - Réduit les appels getInfo() bloquants
    - Vérifications GCS en batch
    - Contrôle du nombre de threads
    - Option pour ne pas attendre la fin des exports

    Args:
        boundry_coords: Coordonnées du polygone
        date_str: Date spécifique (format YYYY-MM-DD)
        start_date, end_date: Période de dates
        indices_list: Liste des indices à calculer
        area_uuid: Identifiant unique de la zone
        max_workers: Nombre maximum de threads parallèles (défaut: 20)
        wait_for_completion: Si True, attend que tous les exports se terminent (défaut: False)

    Returns:
        Dict avec frontend/backend results et optionnellement le statut des tâches
    """
    if indices_list is None:
        indices_list = []

    boundry = ee.Geometry.Polygon(boundry_coords)

    # Période
    if date_str:
        start = ee.Date(date_str)
        end = start.advance(2, 'day')
    elif start_date and end_date:
        start = ee.Date(start_date)
        end = ee.Date(end_date)
    else:
        return {"error": "Veuillez fournir 'date_str' ou 'start_date' et 'end_date'."}

    print(f"🚀 Démarrage du traitement parallélisé avec {max_workers} workers max")

    # Collections Sentinel-2
    s2Sr = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED').filterDate(start, end).filterBounds(boundry)
    s2Clouds = ee.ImageCollection('COPERNICUS/S2_CLOUD_PROBABILITY').filterDate(start, end).filterBounds(boundry)

    joined = ee.Join.saveFirst('cloud_mask').apply(
        s2Sr, s2Clouds, ee.Filter.equals(leftField='system:index', rightField='system:index')
    )

    def mask_clouds(img):
        cloud_prob = ee.Image(img.get('cloud_mask')).select('probability')
        mask = cloud_prob.lt(30)
        return img.updateMask(mask).divide(10000).copyProperties(img, ["system:time_start", "CLOUDY_PIXEL_PERCENTAGE"])

    collection = ee.ImageCollection(joined).map(mask_clouds)

    # ⚡ Optimisation: récupérer la taille une seule fois
    collection_size = collection.size().getInfo()
    if collection_size == 0:
        return {"error": "Aucune image disponible dans cette période."}

    print(f"📊 {collection_size} images trouvées")

    # Garder la meilleure image par jour
    def first_per_day(col):
        days = col.aggregate_array('system:time_start').map(lambda t: ee.Date(t).format('YYYY-MM-dd'))
        unique_days = ee.List(days).distinct()

        def get_best(day):
            day = ee.String(day)
            day_start = ee.Date(day)
            day_end = day_start.advance(1, 'day')
            return col.filterDate(day_start, day_end).sort('CLOUDY_PIXEL_PERCENTAGE').first()

        return ee.ImageCollection(unique_days.map(get_best)).filter(ee.Filter.notNull(['system:time_start']))

    collection = first_per_day(collection)
    imgs_list = collection.toList(collection.size())

    # ⚡ Optimisation: récupérer tous les timestamps en une fois
    timestamps = collection.aggregate_array('system:time_start').getInfo()

    frontend_results = []
    backend_results = []
    all_export_tasks = []  # Liste de tous les exports à lancer

    print(f"⚡ Préparation des tâches d'export...")

    # fonction pour calculer l'indice demandé (identique à l'original)
    def add_index(img, ind):
        if ind == "NDVI":
            return img.normalizedDifference(['B8', 'B4']).rename('NDVI')
        elif ind == "EVI":
            return img.expression(
                '2.5 * ((NIR - RED) / (NIR + 6*RED - 7.5*BLUE + 1))',
                {'NIR': img.select('B8'), 'RED': img.select('B4'), 'BLUE': img.select('B2')}
            ).rename('EVI')
        elif ind == "NDWI":
            return img.normalizedDifference(['B3', 'B8']).rename('NDWI')
        elif ind == "NDMI":
            return img.normalizedDifference(['B8', 'B11']).rename('NDMI')
        elif ind == "MNDWI":
            return img.normalizedDifference(['B3', 'B11']).rename('MNDWI')
        elif ind == "NDRE":
            return img.normalizedDifference(['B8', 'B5']).rename('NDRE')
        elif ind == "NDREX":
            return img.normalizedDifference(['B8A', 'B5']).rename('NDREX')
        elif ind == "CCC":
            return img.expression('(B3 - B4) / (B3 + B4)', {'B3': img.select('B3'), 'B4': img.select('B4')}).rename('CCC')
        elif ind == "CWC":
            return img.expression('B3 / B2', {'B3': img.select('B3'), 'B2': img.select('B2')}).rename('CWC')
        elif ind == "FAPAR":
            return img.expression('(B8 - B4)/(B8 + B4)', {'B8': img.select('B8'), 'B4': img.select('B4')}).rename('FAPAR')
        elif ind == "IRECI":
            return img.expression('(B8 - B4) / (B8 + B4 - B2)', {'B8': img.select('B8'), 'B4': img.select('B4'), 'B2': img.select('B2')}).rename('IRECI')
        elif ind == "LAI":
            return img.expression('3.618 * ((B8/B4) - 0.118)', {'B8': img.select('B8'), 'B4': img.select('B4')}).rename('LAI')
        elif ind == "MSAVI2":
            return img.expression('0.5 * (2*B8 + 1 - sqrt((2*B8 + 1)**2 - 8*(B8 - B4)))', {'B8': img.select('B8'), 'B4': img.select('B4')}).rename('MSAVI2')
        elif ind == "NMDI":
            return img.expression('(B8 - (B11 - B12)) / (B8 + (B11 - B12))', {'B8': img.select('B8'), 'B11': img.select('B11'), 'B12': img.select('B12')}).rename('NMDI')
        elif ind == "SMI":
            return img.expression('(B8 - B11)/(B8 + B11)', {'B8': img.select('B8'), 'B11': img.select('B11')}).rename('SMI')
        elif ind == "SOC_SWIR":
            return img.expression('(B12 - B8)/(B12 + B8)', {'B12': img.select('B12'), 'B8': img.select('B8')}).rename('SOC_SWIR')
        elif ind == "SOC_VIS":
            return img.expression('(B4 - B3)/(B4 + B3)', {'B4': img.select('B4'), 'B3': img.select('B3')}).rename('SOC_VIS')
        elif ind == "WIW":
            return img.expression('(B11 - B12)/(B11 + B12)', {'B11': img.select('B11'), 'B12': img.select('B12')}).rename('WIW')
        elif ind == "SAVI":
            return img.expression('((NIR - RED) / (NIR + RED + 0.5)) * 1.5', {'NIR': img.select('B8'), 'RED': img.select('B4')}).rename('SAVI')
        elif ind == "NBR":
            return img.normalizedDifference(['B8', 'B12']).rename('NBR')
        elif ind == "GCI":
            return img.expression('(NIR / GREEN) - 1', {'NIR': img.select('B8'), 'GREEN': img.select('B3')}).rename('GCI')
        elif ind == "ARVI":
            return img.expression('(NIR - (2*RED - BLUE)) / (NIR + (2*RED - BLUE))', {'NIR': img.select('B8'), 'RED': img.select('B4'), 'BLUE': img.select('B2')}).rename('ARVI')
        elif ind == "Chlorophyll":
            return img.expression('(NIR / RE1) - 1', {'NIR': img.select('B8'), 'RE1': img.select('B5')}).rename('Chlorophyll')
        else:
            return None

    # ⚡ Préparation de toutes les tâches d'export
    band_resolutions = {
        "B1": 60, "B9": 60,
        "B2": 10, "B3": 10, "B4": 10, "B8": 10,
        "B5": 20, "B6": 20, "B7": 20, "B8A": 20, "B11": 20, "B12": 20,
        "AOT": 10, "WVP": 10, "SCL": 20
    }

    # Collecter tous les chemins de fichiers à vérifier
    all_file_paths = []
    export_plan = []  # Plan d'export avec toutes les infos nécessaires

    n_images = len(timestamps)
    for i in range(n_images):
        timestamp = timestamps[i]
        capture_date = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc).strftime("%Y-%m-%d") if timestamp else f"image_{i}"

        # Planifier exports des bandes
        image = ee.Image(collection.toList(1, i).get(0)).clip(boundry)
        band_names = image.bandNames().getInfo()

        for band in band_names:
            scale = band_resolutions.get(band, 10)
            file_name = f"{capture_date}_{band.lower()}_{area_uuid}.tif"
            folder_path = f"{area_uuid}/bandes"
            gcs_path = f"{folder_path}/{file_name}"
            all_file_paths.append(gcs_path)
            export_plan.append({
                'type': 'band',
                'image': image,
                'band': band,
                'scale': scale,
                'capture_date': capture_date,
                'gcs_path': gcs_path
            })

        # Planifier exports des indices
        for ind in indices_list:
            folder_path = f"{area_uuid}/{ind}"
            raw_blob = f"{folder_path}/{capture_date}_raw_{area_uuid}.tif"
            color_blob = f"{folder_path}/{capture_date}_color_{area_uuid}.tif"

            all_file_paths.extend([raw_blob, color_blob])

            export_plan.append({
                'type': 'index',
                'image': image,
                'index': ind,
                'capture_date': capture_date,
                'raw_path': raw_blob,
                'color_path': color_blob
            })

    print(f"📋 {len(export_plan)} tâches d'export planifiées")

    # ⚡ Vérification batch de l'existence des fichiers
    print("🔍 Vérification batch de l'existence des fichiers...")
    file_exists_map = batch_file_exists(BUCKET_NAME, all_file_paths, max_workers=min(max_workers, 50))

    # ⚡ Lancement parallèle de tous les exports nécessaires
    print("🚀 Lancement parallèle des exports...")

    def execute_export_task(task_info):
        """Exécute une tâche d'export selon son type"""
        try:
            if task_info['type'] == 'band':
                if not file_exists_map.get(task_info['gcs_path'], False):
                    return launch_band_export_task(
                        task_info['image'],
                        task_info['band'],
                        area_uuid,
                        task_info['capture_date'],
                        boundry,
                        task_info['scale'],
                        BUCKET_NAME
                    )
                else:
                    print(f"⚠️ Bande déjà présente : {task_info['gcs_path']}")
                    return task_info['gcs_path'], None

            elif task_info['type'] == 'index':
                results = []

                # Export raw si nécessaire
                if not file_exists_map.get(task_info['raw_path'], False):
                    ind_img = add_index(task_info['image'], task_info['index'])
                    if ind_img is not None:
                        raw_result = launch_export_task(
                            ind_img, 'raw', task_info['index'], area_uuid,
                            task_info['capture_date'], boundry, BUCKET_NAME
                        )
                        results.append(raw_result)

                # Export color si nécessaire
                if not file_exists_map.get(task_info['color_path'], False):
                    ind_img = add_index(task_info['image'], task_info['index'])
                    if ind_img is not None:
                        # Calculer les stats si nécessaire pour la colorisation
                        cached_stats = load_statistics(area_uuid, task_info['index'], task_info['capture_date'], BUCKET_NAME)
                        if not cached_stats:
                            try:
                                stats = ind_img.reduceRegion(
                                    reducer=ee.Reducer.percentile([0, 10, 25, 50, 75, 90, 100], None)
                                    .combine(ee.Reducer.mean(), sharedInputs=True)
                                    .combine(ee.Reducer.stdDev(), sharedInputs=True)
                                    .combine(ee.Reducer.minMax(), sharedInputs=True),
                                    geometry=boundry,
                                    scale=10,
                                    bestEffort=True
                                ).getInfo()

                                statistics = {
                                    "min": stats.get(f"{task_info['index']}_min") if stats else None,
                                    "max": stats.get(f"{task_info['index']}_max") if stats else None,
                                    "median": stats.get(f"{task_info['index']}_p50") if stats else None,
                                    "mean": stats.get(f"{task_info['index']}_mean") if stats else None,
                                    "sd": stats.get(f"{task_info['index']}_stdDev") if stats else None,
                                    "percentiles": {
                                        "0.0": stats.get(f"{task_info['index']}_p0") if stats else None,
                                        "10.0": stats.get(f"{task_info['index']}_p10") if stats else None,
                                        "25.0": stats.get(f"{task_info['index']}_p25") if stats else None,
                                        "50.0": stats.get(f"{task_info['index']}_p50") if stats else None,
                                        "75.0": stats.get(f"{task_info['index']}_p75") if stats else None,
                                        "90.0": stats.get(f"{task_info['index']}_p90") if stats else None,
                                        "100.0": stats.get(f"{task_info['index']}_p100") if stats else None,
                                    }
                                }
                                save_statistics(area_uuid, task_info['index'], task_info['capture_date'], statistics, BUCKET_NAME)
                            except Exception as e:
                                print(f"⚠️ Erreur calcul stats pour {task_info['index']}: {e}")
                                statistics = {"min": -1, "max": 1}
                        else:
                            statistics = cached_stats

                        colored_img = colorize_index(ind_img, task_info['index'], statistics)
                        color_result = launch_export_task(
                            colored_img, 'color', task_info['index'], area_uuid,
                            task_info['capture_date'], boundry, BUCKET_NAME
                        )
                        results.append(color_result)

                return results

        except Exception as e:
            print(f"❌ Erreur dans execute_export_task: {e}")
            return None

    # Lancement de tous les exports en parallèle
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_task = {executor.submit(execute_export_task, task): task for task in export_plan}

        for future in as_completed(future_to_task):
            task_info = future_to_task[future]
            try:
                result = future.result()
                if result:
                    if isinstance(result, list):
                        all_export_tasks.extend(result)
                    else:
                        all_export_tasks.append(result)
            except Exception as e:
                print(f"❌ Erreur lors de l'exécution de la tâche {task_info}: {e}")

    print(f"✅ {len(all_export_tasks)} exports lancés en parallèle")

    # Construction des résultats (simplifié pour la version parallèle)
    for i in range(n_images):
        timestamp = timestamps[i]
        capture_date = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc).strftime("%Y-%m-%d") if timestamp else f"image_{i}"

        for ind in indices_list:
            folder_path = f"{area_uuid}/{ind}"
            raw_url = f"https://storage.googleapis.com/{BUCKET_NAME}/{folder_path}/{capture_date}_raw_{area_uuid}.tif"
            color_url = f"https://storage.googleapis.com/{BUCKET_NAME}/{folder_path}/{capture_date}_color_{area_uuid}.tif"

            # Charger les stats si disponibles
            cached_stats = load_statistics(area_uuid, ind, capture_date, BUCKET_NAME)
            statistics = cached_stats if cached_stats else {"min": -1, "max": 1, "mean": 0}

            frontend_results.append({
                "bounds": boundry_coords[0],
                "capture_date": capture_date,
                "data": {
                    "metadata": {"observation_date": capture_date},
                    "raw": raw_url,
                    "color": color_url,
                    "png": color_url,
                    "tiles_color": "NOT_IMPLEMENTED",
                    "tiles_demo": "NOT_IMPLEMENTED",
                    "shp": "NOT_IMPLEMENTED",
                    "geojson": "NOT_IMPLEMENTED",
                    "statistics": "NOT_IMPLEMENTED",
                    "frequencies": "NOT_IMPLEMENTED"
                },
                "statistics": statistics,
                "index": ind,
                "product": "Sentinel-2"
            })

            backend_results.append({
                ind: {
                    "capture_date": capture_date,
                    "raw": raw_url,
                    "color": color_url,
                    "area_uuid": area_uuid,
                    "boundary": boundry_coords[0]
                }
            })

    result = {
        "frontend": frontend_results,
        "backend": backend_results,
        "export_tasks": len(all_export_tasks),
        "parallel_mode": True
    }

    # Suivi optionnel des tâches
    if wait_for_completion and all_export_tasks:
        print("⏳ Attente de la completion des exports...")
        active_tasks = [task for _, task in all_export_tasks if task is not None]

        while any(task.active() for task in active_tasks):
            active_count = sum(1 for task in active_tasks if task.active())
            print(f"   📊 {active_count} tâches encore en cours...")
            time.sleep(30)

        completed = sum(1 for task in active_tasks if task.status().get('state') == 'COMPLETED')
        failed = len(active_tasks) - completed
        print(f"✅ Exports terminés: {completed} réussis, {failed} échecs")

        result["completion_status"] = {
            "completed": completed,
            "failed": failed,
            "total": len(active_tasks)
        }
    else:
        print("🚀 Mode parallèle: tous les exports ont été lancés sans attendre leur completion")

    return result


# -------------------------
# Fonctions utilitaires pour la gestion des tâches
# -------------------------

def get_active_tasks_status(task_list: List[Tuple[str, object]]) -> Dict:
    """
    Retourne le statut des tâches actives sans bloquer.

    Args:
        task_list: Liste de tuples (gcs_path, task)

    Returns:
        Dict avec le statut des tâches
    """
    if not task_list:
        return {"total": 0, "active": 0, "completed": 0, "failed": 0}

    active_tasks = [task for _, task in task_list if task is not None]

    active_count = 0
    completed_count = 0
    failed_count = 0

    for task in active_tasks:
        try:
            if task.active():
                active_count += 1
            else:
                status = task.status()
                if status.get('state') == 'COMPLETED':
                    completed_count += 1
                else:
                    failed_count += 1
        except:
            failed_count += 1

    return {
        "total": len(active_tasks),
        "active": active_count,
        "completed": completed_count,
        "failed": failed_count
    }

def wait_for_tasks_completion(task_list: List[Tuple[str, object]],
                            check_interval: int = 30,
                            max_wait_time: int = 3600,
                            verbose: bool = True) -> Dict:
    """
    Attend la completion de toutes les tâches avec timeout.

    Args:
        task_list: Liste de tuples (gcs_path, task)
        check_interval: Intervalle de vérification en secondes
        max_wait_time: Temps maximum d'attente en secondes
        verbose: Afficher les messages de progression

    Returns:
        Dict avec le statut final
    """
    if not task_list:
        return {"total": 0, "completed": 0, "failed": 0, "timeout": False}

    active_tasks = [task for _, task in task_list if task is not None]
    start_time = time.time()

    if verbose:
        print(f"⏳ Attente de {len(active_tasks)} tâches (timeout: {max_wait_time}s)")

    while time.time() - start_time < max_wait_time:
        status = get_active_tasks_status(task_list)

        if status["active"] == 0:
            if verbose:
                print(f"✅ Toutes les tâches terminées: {status['completed']} réussies, {status['failed']} échecs")
            return {**status, "timeout": False}

        if verbose:
            print(f"   📊 {status['active']} tâches en cours, {status['completed']} terminées, {status['failed']} échecs")

        time.sleep(check_interval)

    # Timeout atteint
    final_status = get_active_tasks_status(task_list)
    if verbose:
        print(f"⏰ Timeout atteint après {max_wait_time}s: {final_status['active']} tâches encore actives")

    return {**final_status, "timeout": True}


def compute_indices_s1(aoi_coords, date_str, indices_list):
    aoi = ee.Geometry.Polygon(aoi_coords)
    start_date = ee.Date(date_str)
    end_date = start_date.advance(5, 'day')

    # Sentinel-1 collection filtrée
    s1_coll = ee.ImageCollection('COPERNICUS/S1_GRD') \
        .filterBounds(aoi) \
        .filterDate(start_date, end_date) \
        .filter(ee.Filter.eq('instrumentMode', 'IW')) \
        .filter(ee.Filter.eq('orbitProperties_pass', 'DESCENDING')) \
        .filter(ee.Filter.listContains('transmitterReceiverPolarisation', 'VV')) \
        .filter(ee.Filter.listContains('transmitterReceiverPolarisation', 'VH'))

    # Vérifier si la collection est vide
    if s1_coll.size().getInfo() == 0:
        return {"error": "Aucune image Sentinel-1 disponible dans cette période."}

    # Image médiane
    s1 = s1_coll.median().clip(aoi)

    # Date de capture
    first_img = s1_coll.first()
    timestamp = first_img.get("system:time_start").getInfo()
    capture_date = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc).strftime("%Y-%m-%d") if timestamp else "Date inconnue"

    # Indices valides pour Sentinel-1
    valid_indices = {
        "RVI": "4 * VH / (VV + VH + 1e-6)",
        "VVVH_Ratio": "VV / (VH + 1e-6)",
        "VVVH_Diff": "VV - VH",
        "SMI": "(VV - VH) / (VV + VH + 1e-6)"
    }

    results = {}
    ignored_indices = []

    for ind in indices_list:
        if ind not in valid_indices:
            results[ind] = {"error": f"Index {ind} non supporté."}
            ignored_indices.append(ind)
            continue

        # Calcul de l'indice
        ind_img = s1.expression(valid_indices[ind], {"VV": s1.select("VV"), "VH": s1.select("VH")}).rename(ind)

        # Statistiques
        stats = ind_img.reduceRegion(
            reducer=ee.Reducer.percentile([0,10,25,50,75,90,100], None)
                    .combine(ee.Reducer.mean(), sharedInputs=True)
                    .combine(ee.Reducer.stdDev(), sharedInputs=True)
                    .combine(ee.Reducer.minMax(), sharedInputs=True),
            geometry=aoi,
            scale=10,  # Résolution native Sentinel-1
            bestEffort=True
        ).getInfo()

        statistics = {}
        if stats:
            statistics = {
                "min": stats.get(f"{ind}_min"),
                "max": stats.get(f"{ind}_max"),
                "median": stats.get(f"{ind}_p50"),
                "mean": stats.get(f"{ind}_mean"),
                "sd": stats.get(f"{ind}_stdDev"),
                "percentiles": {
                    "0.0": stats.get(f"{ind}_p0"),
                    "10.0": stats.get(f"{ind}_p10"),
                    "25.0": stats.get(f"{ind}_p25"),
                    "50.0": stats.get(f"{ind}_p50"),
                    "75.0": stats.get(f"{ind}_p75"),
                    "90.0": stats.get(f"{ind}_p90"),
                    "100.0": stats.get(f"{ind}_p100"),
                }
            }
        else:
            statistics = {"error": "Aucune donnée disponible pour cet index."}

        # URLs de téléchargement et visualisation
        try:
            raw_url = ind_img.getDownloadURL({
                'scale': 10,
                'region': aoi.getInfo()['coordinates'],
                'format': 'GEO_TIFF'
            })
        except:
            raw_url = None

        try:
            png_url = ind_img.visualize(min=-25, max=5, palette=['blue','white','red']).getThumbURL({
                'region': aoi.getInfo(),
                'scale': 10
            })
        except:
            png_url = None

        results[ind] = {
            "bounds": aoi_coords[0],
            "capture_date": capture_date,
            "data": {
                "metadata": {"observation_date": capture_date},
                "raw": raw_url,
                "color": png_url,
                "png": png_url,
                "tiles_color": "NOT_IMPLEMENTED",
                "tiles_demo": "NOT_IMPLEMENTED",
                "shp": "NOT_IMPLEMENTED",
                "geojson": "NOT_IMPLEMENTED",
                "statistics": "NOT_IMPLEMENTED",
                "frequencies": "NOT_IMPLEMENTED"
            },
            "statistics": statistics,
            "index": ind,
            "product": "Sentinel-1"
        }

    if ignored_indices:
        results["warning"] = f"Indices non valides pour Sentinel-1 ignorés: {ignored_indices}"

    return results